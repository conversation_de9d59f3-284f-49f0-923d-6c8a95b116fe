
graph TD
    subgraph "实时任务处理流程 (用户交互 - Fast Path)"
        direction TB
        A[用户请求] --> B[API网关/入口];
        B --> C{主Agent工作流<br/>(e.g., TravelPlannerAgent)};
        
        subgraph "数据交互"
            direction LR
            C -- "1. 读取用户画像/记忆" --> DB_MySQL_Read[(MySQL)];
            C -- "2. 读/写任务状态、短期记忆、缓存" --> DB_Redis[(Redis)];
        end
        
        C --> D[生成最终规划结果/响应];
        
        subgraph "任务完成与数据归档"
            direction LR
            D -- "3. 归档完整执行日志" --> DB_Mongo[(MongoDB)];
            D -- "4. 标记任务完成状态" --> DB_Redis;
            D -- "5. 写入结构化结果" --> DB_MySQL_Write[(MySQL)];
            D --> F[返回结果给用户];
        end
    end

    subgraph "异步记忆沉淀与学习 (后台处理 - Slow Path)"
        direction TD
        
        Trigger[任务完成事件<br/>(Redis Pub/Sub or Stream)] --> H{记忆沉淀服务<br/>(Background Worker)};
        
        subgraph "数据源"
            direction RL
            H -- "1. 读取任务执行日志" --> LogSource(MongoDB);
            H -- "2. 读取现有用户画像/记忆" --> ProfileSource(MySQL);
        end

        subgraph "记忆处理核心三阶段"
            direction TD
            H1[Step 1: 记忆提取<br/>(从日志中识别有价值信息<br/>`extract_user_memories_from_task`)]
            H2[Step 2: 记忆评估<br/>(基于置信度/相关性算法<br/>过滤和评估记忆质量)]
            H3[Step 3: 记忆应用与学习<br/>(将高质量记忆结构化<br/>并更新用户画像<br/>`update_user_profile_with_memories`)]
        end

        H --> H1 --> H2 --> H3;

        subgraph "数据输出"
             direction LR
             H3 -- "写入/更新结构化记忆" --> OutputMemory[(MySQL<br/>user_memories)];
             H3 -- "更新/丰富用户画像" --> OutputProfile[(MySQL<br/>user_profiles)];
        end
    end

    %% 流程连接
    DB_Redis -- "发布任务完成事件" --> Trigger;
    
    %% 样式定义
    classDef main fill:#e8f5e8,stroke:#333,stroke-width:2px;
    classDef async fill:#e1f5fe,stroke:#333,stroke-width:2px;
    classDef db fill:#fff3e0,stroke:#333,stroke-width:1px;
    classDef trigger fill:#ffcdd2,stroke:#c62828,stroke-width:2px;

    class A,B,C,D,F,DB_MySQL_Write main;
    class H,H1,H2,H3 async;
    class DB_MySQL_Read,DB_Redis,DB_Mongo,LogSource,ProfileSource,OutputMemory,OutputProfile db;
    class Trigger trigger;