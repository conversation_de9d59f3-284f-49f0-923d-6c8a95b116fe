
```
graph TD
    subgraph "主任务处理流程 (Fast Path - User Facing)"
        direction LR
        A[用户请求] --> B[API网关];
        B --> C[AutoPilot AI引擎];
        C --> D[生成最终结果];
        D --> E[归档日志到MongoDB];
        D --> F[返回结果给用户<br/>(HTTP 200 OK)];
    end

    subgraph "异步记忆沉淀流程 (Slow Path - Background)"
        direction LR
        G[任务队列 Broker<br/>(如 RabbitMQ / Redis)] --> H[Worker Pool<br/>(独立的后台进程/服务器)];
        subgraph "记忆Agent集群"
            H1[记忆生成Agent];
            H2[记忆评估Agent];
            H3[记忆学习Agent];
        end
        H --> H1 --> H2 --> H3;
        H3 --> I[写入MySQL<br/>(user_memories, user_summaries)];
    end

    E -- "1. 触发异步任务" --> G;
    
    style F fill:#e8f5e8,stroke:#333,stroke-width:2px;
    style G fill:#fff3e0,stroke:#333,stroke-width:2px;
    style H fill:#e1f5fe,stroke:#333,stroke-width:2px;

```