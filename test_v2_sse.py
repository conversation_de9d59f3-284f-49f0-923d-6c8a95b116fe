#!/usr/bin/env python3
"""
V2 SSE API直接测试程序

用于验证V2 API的SSE输出格式是否正确
"""
import requests
import json
import sys

def test_v2_sse_api():
    """测试V2 SSE API"""
    url = "http://localhost:8000/api/travel/v2/plan/stream"

    # 测试请求数据
    test_data = {
        "query": "我想去莆田旅游3天，喜欢自然风光和美食",
        "user_id": "test_user_123",
        "days": 3,
        "budget": 2000,
        "preferences": ["自然风光", "美食"]
    }

    print("=== V2 SSE API 测试 ===")
    print(f"URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("\n=== SSE 事件流 ===")

    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Accept': 'text/event-stream'},
            stream=True
        )

        if response.status_code != 200:
            print(f"错误: HTTP {response.status_code}")
            print(f"响应: {response.text}")
            return

        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        print("\n--- 原始SSE数据 ---")

        event_count = 0
        for line in response.iter_lines(decode_unicode=True):
            if line:
                # 打印原始行
                print(f"原始行: {repr(line)}")

                # 如果是SSE数据行
                if line.startswith('data: '):
                    event_count += 1
                    print(f"\n--- 事件 #{event_count} ---")

                    # 提取JSON部分
                    json_part = line[6:].strip()

                    print(f"JSON部分: {repr(json_part)}")

                    # 尝试解析JSON
                    try:
                        event_data = json.loads(json_part)
                        print(f"解析成功: {json.dumps(event_data, ensure_ascii=False, indent=2)}")
                    except json.JSONDecodeError as e:
                        print(f"JSON解析失败: {e}")
                        print(f"原始JSON: {json_part}")

                    # 检查是否是结束事件
                    if '"event": "eos"' in json_part:
                        print("\n收到结束事件，停止监听")
                        break

                # 限制事件数量，避免无限循环
                if event_count >= 20:
                    print("\n达到最大事件数量限制，停止监听")
                    break

        print(f"\n=== 测试完成，共收到 {event_count} 个事件 ===")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("启动V2 SSE API测试...")
    test_v2_sse_api()
