#!/usr/bin/env python3
"""
V2 SSE API直接测试程序

用于验证V2 API的SSE输出格式是否正确
"""
import requests
import json
import sys

def test_v2_sse_api():
    """测试V2 SSE API"""
    url = "http://localhost:8000/api/travel/v2/plan/stream"

    # 测试请求数据
    test_data = {
        "query": "我想去莆田旅游1天，测试接口",
        "user_id": "test_user_direct"
    }

    print("=== V2 SSE API 直接测试 ===")
    print(f"URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    print("\n=== SSE 事件流 ===")

    try:
        response = requests.post(
            url,
            json=test_data,
            headers={'Accept': 'text/event-stream'},
            stream=True,
            timeout=30
        )

        if response.status_code != 200:
            print(f"错误: HTTP {response.status_code}")
            print(f"响应: {response.text}")
            return

        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        print("\n--- 原始SSE数据 ---")

        event_count = 0
        for line in response.iter_lines(decode_unicode=True):
            if line:
                event_count += 1
                print(f"\n[{event_count}] 原始行: {repr(line)}")

                # 检查是否是重复的data:前缀
                if line.startswith('data: data: '):
                    print("❌ 发现重复的data:前缀！")
                    json_part = line[12:].strip()  # 移除 "data: data: "
                    print(f"修正后的JSON: {repr(json_part)}")
                elif line.startswith('data: '):
                    print("✅ 正确的SSE格式")
                    json_part = line[6:].strip()  # 移除 "data: "
                    print(f"JSON部分: {repr(json_part)}")
                else:
                    print("ℹ️ 非data行，跳过")
                    continue

                # 尝试解析JSON
                if json_part:
                    try:
                        event_data = json.loads(json_part)
                        event_type = event_data.get('event', 'unknown')
                        print(f"✅ JSON解析成功，事件类型: {event_type}")

                        # 只打印关键信息，避免输出过长
                        if event_type == 'step_start':
                            step_name = event_data.get('data', {}).get('step_name', 'unknown')
                            title = event_data.get('data', {}).get('title', 'unknown')
                            print(f"   步骤开始: {step_name} - {title}")
                        elif event_type == 'step_end':
                            step_name = event_data.get('data', {}).get('step_name', 'unknown')
                            status = event_data.get('data', {}).get('status', 'unknown')
                            print(f"   步骤结束: {step_name} - {status}")
                        elif event_type == 'complete':
                            title = event_data.get('data', {}).get('title', 'unknown')
                            print(f"   规划完成: {title}")
                        elif event_type == 'eos':
                            print("   流结束")
                            break

                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        print(f"   原始JSON: {json_part[:100]}...")

                # 限制事件数量
                if event_count >= 30:
                    print("\n达到最大事件数量限制，停止监听")
                    break

        print(f"\n=== 测试完成，共收到 {event_count} 个事件 ===")

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("启动V2 SSE API直接测试...")
    test_v2_sse_api()
