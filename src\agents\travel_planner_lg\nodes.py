"""
LangGraph 节点函数实现 (V2.0 - 集成NotificationService)

每个节点函数负责执行特定的任务步骤，并更新状态。
集成了实时通知服务，支持事件驱动的前端UI更新。
"""
import asyncio
from typing import Dict, Any, List
from .state import TravelPlanState, CityPlanResult
from src.core.logger import get_logger
from src.services.notification_service import NotificationService
from src.agents.services import ReasoningService, UserProfileService, AmapService, AnalysisService

logger = get_logger("travel_planner_nodes")


async def analyze_core_intent(state: TravelPlanState) -> Dict[str, Any]:
    """分析核心意图节点
    
    解析用户的原始查询，提取目的地、时间、偏好等核心信息。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)  # 任务ID，用于Redis键
    notification_service = state.get('notification_service')
    
    step_name = "core_intent_analysis"
    step_title = "解析用户需求和画像"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在分析您的旅行意图和需求..."
            )
        
        logger.info(f"[{trace_id}] 开始分析核心意图")
        
        # 使用服务进行分析
        reasoning_service = ReasoningService()
        user_profile_service = UserProfileService()

        # 获取用户画像和记忆
        user_profile = await user_profile_service.get_user_profile(state['user_id'])
        user_memories = await user_profile_service.get_user_memories(state['user_id'])

        # 调用分析服务
        core_intent = await reasoning_service.analyze_core_intent(
            state['original_query'],
            user_profile,
            user_memories
        )

        destinations = core_intent.get("destinations", [])
        if not destinations:
            raise ValueError("未能从用户查询中提取有效目的地")

        # 生成旁白文本
        narration_text = f"我理解您想要规划一个{len(destinations)}个城市的旅行，主要目的地包括：{', '.join(destinations)}"
        
        # 构建结果
        result = {
            "destinations": destinations,
            "core_intent": core_intent,
            "current_narration_text": narration_text,
            "current_step": "核心意图分析完成",
            "progress_percentage": 20
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": f"识别出{len(destinations)}个目的地：{', '.join(destinations)}",
                    "destinations": destinations,
                    "duration_days": core_intent['duration_days']
                }
            )
        
        logger.info(f"[{trace_id}] 核心意图分析完成，目的地：{destinations}")
        return result
        
    except Exception as e:
        error_msg = f"核心意图分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        # 返回错误状态
        return {
            "error": error_msg,
            "current_step": "核心意图分析失败",
            "progress_percentage": state.get("progress_percentage", 0)
        }


async def analyze_multi_city_strategy(state: TravelPlanState) -> Dict[str, Any]:
    """分析多城市策略节点
    
    如果识别出多个目的地，则生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "multi_city_strategy"
    step_title = "多城市策略分析"
    
    try:
        destinations = state.get("destinations", [])
        
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, 
                f"正在为{len(destinations)}个目的地制定旅行策略..."
            )
        
        logger.info(f"[{trace_id}] 开始分析多城市策略")
        
        if len(destinations) > 1:
            # 使用服务进行分析
            reasoning_service = ReasoningService()
            strategy = await reasoning_service.analyze_multi_city_strategy(
                destinations=destinations,
                core_intent=state.get("core_intent")
            )
            
            narration_text = strategy.get("narration", f"建议您按照 {' -> '.join(destinations)} 的顺序游览。")
            
            # 根据模式决定是否需要澄清
            needs_clarification = state.get("execution_mode") == "interactive"
            
            result = {
                "multi_city_strategy": strategy,
                "current_narration_text": narration_text,
                "clarification_needed": "multi_city_strategy" if needs_clarification else None,
                "current_step": "多城市策略分析完成",
                "progress_percentage": 30
            }
            
            # 发送步骤完成事件
            if notification_service:
                await notification_service.notify_step_end(
                    task_id, step_name, "success", {
                        "content": f"制定了{len(destinations)}城市策略：{narration_text}",
                        "strategy": strategy
                    }
                )
        else:
            # 单目的地，直接跳过
            result = {
                "current_step": "单目的地，跳过多城市策略",
                "progress_percentage": 30
            }
            
            # 发送步骤完成事件
            if notification_service:
                await notification_service.notify_step_end(
                    task_id, step_name, "success", {
                        "content": "单目的地旅行，无需多城市策略"
                    }
                )
        
        logger.info(f"[{trace_id}] 多城市策略分析完成")
        return result
        
    except Exception as e:
        error_msg = f"多城市策略分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "多城市策略分析失败",
            "progress_percentage": state.get("progress_percentage", 20)
        }


async def analyze_driving_context(state: TravelPlanState) -> Dict[str, Any]:
    """分析驾驶情境节点
    
    分析用户的车辆信息和驾驶需求。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "driving_context_analysis"
    step_title = "驾驶情境分析"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在分析您的车辆信息和驾驶需求..."
            )
        
        logger.info(f"[{trace_id}] 开始分析驾驶情境")
        
        # 使用服务获取信息和分析
        user_profile_service = UserProfileService()
        reasoning_service = ReasoningService()

        vehicle_info = await user_profile_service.get_vehicle_info(state['user_id'])
        
        driving_context = await reasoning_service.analyze_driving_context(
            vehicle_info,
            state.get("core_intent")
        )
        
        narration_text = driving_context.get("narration", "我会为您的自驾行程提供相关辅助信息。")
        
        result = {
            "user_vehicle_info": vehicle_info,
            "driving_strategy": driving_context.get("strategy", "general_assistance"),
            "planning_range_km": driving_context.get("planning_range_km"),
            "range_buffer_factor": driving_context.get("range_buffer_factor", 0.8),
            "current_narration_text": narration_text,
            "current_step": "驾驶情境分析完成",
            "progress_percentage": 40
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": narration_text,
                    "vehicle_model": vehicle_info.get("model"),
                    "driving_strategy": driving_context.get("strategy")
                }
            )
        
        logger.info(f"[{trace_id}] 驾驶情境分析完成，策略：{driving_context.get('strategy')}")
        return result
        
    except Exception as e:
        error_msg = f"驾驶情境分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "驾驶情境分析失败",
            "progress_percentage": state.get("progress_percentage", 30)
        }


async def analyze_preferences(state: TravelPlanState) -> Dict[str, Any]:
    """分析用户偏好节点
    
    基于用户画像和记忆，分析用户的旅行偏好。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "preference_analysis"
    step_title = "用户偏好分析"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在分析您的个性化旅行偏好..."
            )
        
        logger.info(f"[{trace_id}] 开始分析用户偏好")
        
        # 使用服务进行分析
        reasoning_service = ReasoningService()
        
        preferences = await reasoning_service.analyze_preferences(
            state.get("core_intent"),
            state.get("user_profile"),
            state.get("retrieved_memories")
        )
        
        narration_text = preferences.get("narration", "已完成您的个性化偏好分析。")

        result = {
            "attraction_preferences": preferences.get("attraction"),
            "food_preferences": preferences.get("food"),
            "accommodation_preferences": preferences.get("accommodation"),
            "current_narration_text": narration_text,
            "current_step": "用户偏好分析完成",
            "progress_percentage": 50
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": narration_text,
                    "attraction_types": result["attraction_preferences"]["types"],
                    "food_types": result["food_preferences"]["cuisine_types"],
                    "accommodation_type": result["accommodation_preferences"]["type"]
                }
            )
        
        logger.info(f"[{trace_id}] 用户偏好分析完成")
        return result
        
    except Exception as e:
        error_msg = f"用户偏好分析失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "用户偏好分析失败",
            "progress_percentage": state.get("progress_percentage", 40)
        }


async def execute_planning_stage(state: TravelPlanState) -> Dict[str, Any]:
    """执行规划阶段节点
    
    调用高德地图等工具进行POI搜索、路线规划，并生成每日行程。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "planning_execution"
    step_title = "执行旅行规划"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在为您搜索景点、美食、酒店并规划路线..."
            )
        
        logger.info(f"[{trace_id}] 开始执行规划阶段")
        
        amap_service = AmapService()
        analysis_service = AnalysisService()
        
        destinations = state.get("destinations", [])
        city_plans: List[CityPlanResult] = []

        for city in destinations:
            # 1. 搜索POI
            pois = await amap_service.search_pois_for_city(
                city=city,
                attraction_preferences=state.get("attraction_preferences"),
                food_preferences=state.get("food_preferences"),
                accommodation_preferences=state.get("accommodation_preferences")
            )
            
            # 2. POI评分
            scored_pois = await analysis_service.score_pois(
                pois, state.get("attraction_preferences")
            )
            
            # 3. 编排行程
            daily_itinerary = await analysis_service.orchestrate_daily_itinerary(
                city=city,
                days=state.get("core_intent", {}).get("duration_days", 1),
                scored_pois=scored_pois
            )
            
            city_plans.append(CityPlanResult(
                city=city,
                attractions=[p for p in scored_pois if p.get("type") == "attraction"],
                foods=[p for p in scored_pois if p.get("type") == "food"],
                accommodations=[p for p in scored_pois if p.get("type") == "accommodation"],
                daily_itinerary=daily_itinerary
            ))

        # 4. 路线规划 (简化为总体)
        driving_routes = await amap_service.plan_driving_routes(
            origin="用户当前位置", # 需要从state获取
            destinations=destinations
        )

        narration_text = f"已为您在{', '.join(destinations)}规划了详细行程，包含景点、美食和住宿。"

        result = {
            "city_plan_results": [cp.model_dump() for cp in city_plans],
            "driving_routes": driving_routes,
            "current_narration_text": narration_text,
            "current_step": "规划阶段执行完成",
            "progress_percentage": 80
        }
        
        # 发送步骤完成事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id, step_name, "success", {
                    "content": narration_text,
                    "total_pois": sum(len(cp.attractions) + len(cp.foods) for cp in city_plans)
                }
            )
        
        logger.info(f"[{trace_id}] 规划阶段执行完成")
        return result
        
    except Exception as e:
        error_msg = f"规划阶段执行失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "规划阶段执行失败",
            "progress_percentage": state.get("progress_percentage", 50)
        }


async def finalize_result(state: TravelPlanState) -> Dict[str, Any]:
    """最终化结果节点
    
    整合所有信息，生成最终的结构化行程。
    """
    trace_id = state.get('trace_id', 'unknown')
    task_id = state.get('task_id', trace_id)
    notification_service = state.get('notification_service')
    
    step_name = "result_finalization"
    step_title = "完成旅行规划"
    
    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id, step_name, step_title, "正在为您生成最终的旅行方案..."
            )
        
        logger.info(f"[{trace_id}] 开始最终化结果")
        
        # 使用服务生成最终行程
        reasoning_service = ReasoningService()
        final_itinerary = await reasoning_service.generate_final_itinerary(
            state.get("core_intent"),
            state.get("city_plan_results"),
            state.get("driving_routes")
        )

        narration_text = final_itinerary.get("summary", "您的专属旅行计划已生成！")
        
        result = {
            "final_itinerary": final_itinerary,
            "current_narration_text": narration_text,
            "current_step": "旅行规划完成",
            "progress_percentage": 100
        }
        
        # 发送最终结果事件
        if notification_service:
            await notification_service.notify_final_result(
                task_id, final_itinerary, narration_text
            )
        
        logger.info(f"[{trace_id}] 旅行规划完成")
        return result
        
    except Exception as e:
        error_msg = f"结果最终化失败: {str(e)}"
        logger.error(f"[{trace_id}] {error_msg}")
        
        # 发送错误事件
        if notification_service:
            await notification_service.notify_error(task_id, error_msg, step_name)
        
        return {
            "error": error_msg,
            "current_step": "结果最终化失败",
            "progress_percentage": state.get("progress_percentage", 80)
        }


def wait_for_user_input(state: TravelPlanState) -> Dict[str, Any]:
    """等待用户输入节点
    
    在交互模式下暂停执行，等待用户确认。
    """
    trace_id = state.get('trace_id', 'unknown')
    logger.info(f"[{trace_id}] 等待用户输入")
    
    # 这个节点主要是标记状态，实际的等待逻辑在图的执行层处理
    return {
        "current_step": "等待用户确认",
        "progress_percentage": state.get("progress_percentage", 50)
    }


def route_after_analysis(state: TravelPlanState) -> str:
    """分析后的路由函数
    
    决定下一步走向：是否需要等待用户输入。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"


def decide_planning_or_end(state: TravelPlanState) -> str:
    """决定是继续规划还是结束
    
    在分析的最后阶段决定是继续规划还是结束。
    """
    # 检查是否有错误
    if state.get("error"):
        return "__end__"
    
    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"
    
    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"
